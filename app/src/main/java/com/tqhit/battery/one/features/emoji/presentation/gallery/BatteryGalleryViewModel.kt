package com.tqhit.battery.one.features.emoji.presentation.gallery

import android.content.Context
import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory
import com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase
import com.tqhit.battery.one.repository.AppRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for the Battery Gallery screen.
 * Manages UI state and handles user interactions following MVI pattern.
 * 
 * This ViewModel follows the established patterns in the app:
 * - Uses Hilt for dependency injection
 * - Implements MVI pattern with StateFlow
 * - Integrates with existing repositories and use cases
 * - Handles loading states, errors, and data transformations
 * - Supports category filtering, search, and premium unlock flows
 */
@HiltViewModel
class BatteryGalleryViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    private val getBatteryStylesUseCase: GetBatteryStylesUseCase,
    private val appRepository: AppRepository
) : ViewModel() {
    
    companion object {
        private const val TAG = "BatteryGalleryVM"
    }
    
    // Private mutable state
    private val _uiState = MutableStateFlow(BatteryGalleryState.initial())
    
    // Public read-only state
    val uiState: StateFlow<BatteryGalleryState> = _uiState.asStateFlow()
    
    init {
        Log.d(TAG, "BatteryGalleryViewModel initialized")
        observeBatteryStyles()
        loadInitialData()
    }
    
    /**
     * Handles events from the UI
     */
    fun handleEvent(event: BatteryGalleryEvent) {
        Log.d(TAG, "Handling event: ${event::class.simpleName}")
        
        when (event) {
            is BatteryGalleryEvent.LoadInitialData -> loadInitialData()
            is BatteryGalleryEvent.RefreshData -> refreshData()
            is BatteryGalleryEvent.RetryLoad -> retryLoad()
            is BatteryGalleryEvent.SelectCategory -> selectCategory(event.category)
            is BatteryGalleryEvent.SearchQueryChanged -> updateSearchQuery(event.query)
            is BatteryGalleryEvent.ToggleSearchMode -> toggleSearchMode()
            is BatteryGalleryEvent.ClearSearch -> clearSearch()
            is BatteryGalleryEvent.SelectBatteryStyle -> selectBatteryStyle(event.style)
            is BatteryGalleryEvent.ToggleGlobalFeature -> toggleGlobalFeature(event.isEnabled)
            is BatteryGalleryEvent.TogglePremiumFilter -> togglePremiumFilter()
            is BatteryGalleryEvent.ToggleFreeFilter -> toggleFreeFilter()
            is BatteryGalleryEvent.ClearAllFilters -> clearAllFilters()
            is BatteryGalleryEvent.UnlockPremiumStyle -> unlockPremiumStyle(event.style)
            is BatteryGalleryEvent.WatchAdToUnlock -> watchAdToUnlock(event.style)
            is BatteryGalleryEvent.PurchasePremiumUnlock -> purchasePremiumUnlock(event.style)
            is BatteryGalleryEvent.NavigationEvent -> handleNavigationEvent(event)
            is BatteryGalleryEvent.ErrorEvent -> handleErrorEvent(event)
            is BatteryGalleryEvent.UIEvent -> handleUIEvent(event)
            is BatteryGalleryEvent.AdEvent -> handleAdEvent(event)
            is BatteryGalleryEvent.SystemEvent -> handleSystemEvent(event)
        }
    }
    
    /**
     * Observes battery styles from the use case
     */
    private fun observeBatteryStyles() {
        viewModelScope.launch {
            combine(
                getBatteryStylesUseCase.batteryStylesFlow,
                getBatteryStylesUseCase.isLoadingFlow
            ) { styles, isLoading ->
                Pair(styles, isLoading)
            }.catch { exception ->
                Log.e(TAG, "Error observing battery styles", exception)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "Failed to load battery styles: ${exception.message}"
                )
            }.collect { (styles, isLoading) ->
                Log.d(TAG, "Received ${styles.size} styles, loading: $isLoading")
                updateStateWithStyles(styles, isLoading)
            }
        }
    }
    
    /**
     * Loads initial data
     */
    private fun loadInitialData() {
        Log.d(TAG, "Loading initial data")
        _uiState.value = _uiState.value.copy(isLoading = true, errorMessage = null)
        
        viewModelScope.launch {
            try {
                val styles = getBatteryStylesUseCase.getAllStyles()
                Log.d(TAG, "Loaded ${styles.size} initial styles")
                updateStateWithStyles(styles, false)
            } catch (exception: Exception) {
                Log.e(TAG, "Failed to load initial data", exception)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "Failed to load battery styles: ${exception.message}",
                    isInitialLoadComplete = true
                )
            }
        }
    }
    
    /**
     * Refreshes data from remote config
     */
    private fun refreshData() {
        Log.d(TAG, "Refreshing data")
        _uiState.value = _uiState.value.copy(isRefreshing = true, errorMessage = null)
        
        viewModelScope.launch {
            try {
                val success = getBatteryStylesUseCase.refreshStyles()
                Log.d(TAG, "Refresh completed, success: $success")
                _uiState.value = _uiState.value.copy(isRefreshing = false)
            } catch (exception: Exception) {
                Log.e(TAG, "Failed to refresh data", exception)
                _uiState.value = _uiState.value.copy(
                    isRefreshing = false,
                    errorMessage = "Failed to refresh: ${exception.message}"
                )
            }
        }
    }
    
    /**
     * Retries loading after an error
     */
    private fun retryLoad() {
        Log.d(TAG, "Retrying load")
        loadInitialData()
    }
    
    /**
     * Selects a category for filtering
     */
    private fun selectCategory(category: BatteryStyleCategory) {
        Log.d(TAG, "Selecting category: ${category.displayName}")
        val currentState = _uiState.value
        val filteredStyles = filterStylesByCategory(currentState.allStyles, category)
        
        _uiState.value = currentState.copy(
            selectedCategory = category,
            displayedStyles = filteredStyles,
            searchQuery = "", // Clear search when changing category
            isSearchActive = false
        )
    }
    
    /**
     * Updates the search query and filters styles
     */
    private fun updateSearchQuery(query: String) {
        Log.d(TAG, "Updating search query: $query")
        val currentState = _uiState.value
        val filteredStyles = if (query.isBlank()) {
            filterStylesByCategory(currentState.allStyles, currentState.selectedCategory)
        } else {
            currentState.allStyles.filter { style ->
                style.matchesSearch(query) && 
                (currentState.selectedCategory == BatteryStyleCategory.HOT && style.isPopular ||
                 currentState.selectedCategory != BatteryStyleCategory.HOT && style.category == currentState.selectedCategory)
            }
        }
        
        _uiState.value = currentState.copy(
            searchQuery = query,
            displayedStyles = applyAdditionalFilters(filteredStyles, currentState)
        )
    }
    
    /**
     * Toggles search mode
     */
    private fun toggleSearchMode() {
        val currentState = _uiState.value
        _uiState.value = currentState.copy(
            isSearchActive = !currentState.isSearchActive,
            searchQuery = if (!currentState.isSearchActive) "" else currentState.searchQuery
        )
    }
    
    /**
     * Clears search and exits search mode
     */
    private fun clearSearch() {
        val currentState = _uiState.value
        val filteredStyles = filterStylesByCategory(currentState.allStyles, currentState.selectedCategory)
        
        _uiState.value = currentState.copy(
            searchQuery = "",
            isSearchActive = false,
            displayedStyles = applyAdditionalFilters(filteredStyles, currentState)
        )
    }
    
    /**
     * Handles battery style selection
     */
    private fun selectBatteryStyle(style: BatteryStyle) {
        Log.d(TAG, "Selected battery style: ${style.name}")
        // This will be handled by the fragment for navigation
    }
    
    /**
     * Toggles the global emoji battery feature
     */
    private fun toggleGlobalFeature(isEnabled: Boolean) {
        Log.d(TAG, "Toggling global feature: $isEnabled")
        _uiState.value = _uiState.value.copy(isGlobalToggleEnabled = isEnabled)
        // TODO: Save to preferences and start/stop overlay service
    }
    
    /**
     * Toggles premium filter
     */
    private fun togglePremiumFilter() {
        val currentState = _uiState.value
        val newShowOnlyPremium = !currentState.showOnlyPremium
        val filteredStyles = filterStyles(currentState.allStyles, currentState.selectedCategory, currentState.searchQuery)
        
        _uiState.value = currentState.copy(
            showOnlyPremium = newShowOnlyPremium,
            showOnlyFree = if (newShowOnlyPremium) false else currentState.showOnlyFree,
            displayedStyles = applyAdditionalFilters(filteredStyles, currentState.copy(showOnlyPremium = newShowOnlyPremium))
        )
    }
    
    /**
     * Toggles free filter
     */
    private fun toggleFreeFilter() {
        val currentState = _uiState.value
        val newShowOnlyFree = !currentState.showOnlyFree
        val filteredStyles = filterStyles(currentState.allStyles, currentState.selectedCategory, currentState.searchQuery)
        
        _uiState.value = currentState.copy(
            showOnlyFree = newShowOnlyFree,
            showOnlyPremium = if (newShowOnlyFree) false else currentState.showOnlyPremium,
            displayedStyles = applyAdditionalFilters(filteredStyles, currentState.copy(showOnlyFree = newShowOnlyFree))
        )
    }
    
    /**
     * Clears all filters
     */
    private fun clearAllFilters() {
        val currentState = _uiState.value
        val filteredStyles = filterStylesByCategory(currentState.allStyles, currentState.selectedCategory)
        
        _uiState.value = currentState.copy(
            searchQuery = "",
            isSearchActive = false,
            showOnlyPremium = false,
            showOnlyFree = false,
            displayedStyles = filteredStyles
        )
    }
    
    /**
     * Handles premium style unlock
     */
    private fun unlockPremiumStyle(style: BatteryStyle) {
        Log.d(TAG, "Unlocking premium style: ${style.name}")
        // TODO: Implement premium unlock logic
    }
    
    /**
     * Handles watching ad to unlock premium style
     */
    private fun watchAdToUnlock(style: BatteryStyle) {
        Log.d(TAG, "Watching ad to unlock style: ${style.name}")
        // TODO: Implement ad unlock logic
    }
    
    /**
     * Handles purchasing premium unlock
     */
    private fun purchasePremiumUnlock(style: BatteryStyle) {
        Log.d(TAG, "Purchasing premium unlock for style: ${style.name}")
        // TODO: Implement purchase logic
    }
    
    /**
     * Updates state with new styles and loading state
     */
    private fun updateStateWithStyles(styles: List<BatteryStyle>, isLoading: Boolean) {
        val currentState = _uiState.value
        val filteredStyles = if (styles.isNotEmpty()) {
            val categoryFiltered = filterStylesByCategory(styles, currentState.selectedCategory)
            val searchFiltered = if (currentState.searchQuery.isNotBlank()) {
                categoryFiltered.filter { it.matchesSearch(currentState.searchQuery) }
            } else {
                categoryFiltered
            }
            applyAdditionalFilters(searchFiltered, currentState)
        } else {
            emptyList()
        }
        
        _uiState.value = currentState.copy(
            isLoading = isLoading,
            isInitialLoadComplete = true,
            allStyles = styles,
            displayedStyles = filteredStyles,
            errorMessage = null,
            lastRefreshTimestamp = if (!isLoading) System.currentTimeMillis() else currentState.lastRefreshTimestamp
        )
    }
    
    /**
     * Filters styles by category
     */
    private fun filterStylesByCategory(styles: List<BatteryStyle>, category: BatteryStyleCategory): List<BatteryStyle> {
        return when (category) {
            BatteryStyleCategory.HOT -> styles.filter { it.isPopular }
            else -> styles.filter { it.category == category }
        }
    }
    
    /**
     * Filters styles by category and search query
     */
    private fun filterStyles(styles: List<BatteryStyle>, category: BatteryStyleCategory, searchQuery: String): List<BatteryStyle> {
        val categoryFiltered = filterStylesByCategory(styles, category)
        return if (searchQuery.isNotBlank()) {
            categoryFiltered.filter { it.matchesSearch(searchQuery) }
        } else {
            categoryFiltered
        }
    }
    
    /**
     * Applies additional filters (premium/free)
     */
    private fun applyAdditionalFilters(styles: List<BatteryStyle>, state: BatteryGalleryState): List<BatteryStyle> {
        return when {
            state.showOnlyPremium -> styles.filter { it.isPremium }
            state.showOnlyFree -> styles.filter { !it.isPremium }
            else -> styles
        }
    }
    
    /**
     * Handles navigation events
     */
    private fun handleNavigationEvent(event: BatteryGalleryEvent.NavigationEvent) {
        Log.d(TAG, "Handling navigation event: ${event::class.simpleName}")
        // Navigation will be handled by the fragment
    }
    
    /**
     * Handles error events
     */
    private fun handleErrorEvent(event: BatteryGalleryEvent.ErrorEvent) {
        when (event) {
            is BatteryGalleryEvent.ErrorEvent.ShowError -> {
                _uiState.value = _uiState.value.copy(errorMessage = event.message)
            }
            is BatteryGalleryEvent.ErrorEvent.DismissError -> {
                _uiState.value = _uiState.value.copy(errorMessage = null)
            }
            is BatteryGalleryEvent.ErrorEvent.ShowToast -> {
                // Toast will be handled by the fragment
            }
        }
    }
    
    /**
     * Handles UI events
     */
    private fun handleUIEvent(event: BatteryGalleryEvent.UIEvent) {
        Log.d(TAG, "Handling UI event: ${event::class.simpleName}")
        // UI events will be handled by the fragment
    }
    
    /**
     * Handles ad events
     */
    private fun handleAdEvent(event: BatteryGalleryEvent.AdEvent) {
        Log.d(TAG, "Handling ad event: ${event::class.simpleName}")
        // Ad events will be handled by the fragment with ad managers
    }
    
    /**
     * Handles system events
     */
    private fun handleSystemEvent(event: BatteryGalleryEvent.SystemEvent) {
        Log.d(TAG, "Handling system event: ${event::class.simpleName}")
        when (event) {
            is BatteryGalleryEvent.SystemEvent.OnResume -> {
                // Refresh data if needed
                if (!getBatteryStylesUseCase.hasCachedData()) {
                    loadInitialData()
                }
            }
            else -> {
                // Other system events handled as needed
            }
        }
    }
}
