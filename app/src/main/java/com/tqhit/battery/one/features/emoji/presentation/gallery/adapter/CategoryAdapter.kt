package com.tqhit.battery.one.features.emoji.presentation.gallery.adapter

import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.tqhit.battery.one.databinding.ItemCategoryBinding
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory

/**
 * RecyclerView adapter for displaying battery style categories.
 * Follows the established patterns from the animation CategoryAdapter.
 * 
 * This adapter:
 * - Uses ViewBinding for view access
 * - Handles category selection with visual feedback
 * - Shows category emoji and display name
 * - Supports horizontal scrolling layout
 */
class CategoryAdapter(
    private val categories: List<BatteryStyleCategory>,
    private var selectedIndex: Int,
    private val onCategorySelected: (Int) -> Unit
) : RecyclerView.Adapter<CategoryViewHolder>() {
    
    companion object {
        private const val TAG = "CategoryAdapter"
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CategoryViewHolder {
        val binding = ItemCategoryBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return CategoryViewHolder(binding)
    }
    
    override fun onBindViewHolder(holder: CategoryViewHolder, position: Int) {
        holder.bind(
            categories[position],
            position == selectedIndex,
            onCategorySelected
        )
    }
    
    override fun getItemCount(): Int = categories.size
    
    /**
     * Updates the selected category index
     */
    fun updateSelection(newIndex: Int) {
        if (newIndex != selectedIndex && newIndex in 0 until categories.size) {
            val oldIndex = selectedIndex
            selectedIndex = newIndex
            
            // Notify only the changed items for better performance
            notifyItemChanged(oldIndex)
            notifyItemChanged(selectedIndex)
            
            Log.d(TAG, "Category selection updated from $oldIndex to $selectedIndex")
        }
    }
    
    /**
     * Gets the currently selected category
     */
    fun getSelectedCategory(): BatteryStyleCategory? {
        return if (selectedIndex in 0 until categories.size) {
            categories[selectedIndex]
        } else {
            null
        }
    }
}

/**
 * ViewHolder for category items
 */
class CategoryViewHolder(private val binding: ItemCategoryBinding) : RecyclerView.ViewHolder(binding.root) {
    
    companion object {
        private const val TAG = "CategoryViewHolder"
    }
    
    fun bind(
        category: BatteryStyleCategory,
        isSelected: Boolean,
        onCategorySelected: (Int) -> Unit
    ) {
        try {
            // Set category text with emoji
            binding.categoryName.text = category.getDisplayText()

            // Update selection state
            updateSelectionState(isSelected)

            // Set click listener
            binding.categoryBlock.setOnClickListener {
                Log.d(TAG, "Category clicked: ${category.displayName}")
                onCategorySelected(adapterPosition)
            }

            Log.d(TAG, "Bound category: ${category.displayName}, selected: $isSelected")
        } catch (exception: Exception) {
            Log.e(TAG, "Error binding category: ${category.displayName}", exception)
        }
    }

    /**
     * Updates the visual selection state
     */
    private fun updateSelectionState(isSelected: Boolean) {
        binding.categoryName.isSelected = isSelected

        // Update background based on selection
        if (isSelected) {
            binding.categoryBlock.setBackgroundResource(com.tqhit.battery.one.R.drawable.grey_block_line_up_selected)
        } else {
            binding.categoryBlock.setBackgroundResource(com.tqhit.battery.one.R.drawable.grey_block_line_up)
        }
    }
}
