package com.tqhit.battery.one.features.emoji.presentation.overlay

import android.content.Context
import android.content.Intent
import android.util.Log
import com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository
import com.tqhit.battery.one.features.emoji.presentation.overlay.permission.EmojiOverlayPermissionManager
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Helper class for managing the emoji battery overlay service lifecycle.
 * Provides centralized control for starting, stopping, and monitoring the service.
 * 
 * This helper follows the established patterns in the app:
 * - Uses Hilt for dependency injection
 * - Integrates with existing repository pattern
 * - Provides comprehensive logging and error handling
 * - Follows service management patterns from other features
 * - Supports reactive configuration updates
 */
@Singleton
class EmojiBatteryServiceHelper @Inject constructor(
    @ApplicationContext private val context: Context,
    private val customizationRepository: CustomizationRepository
) {
    
    companion object {
        private const val TAG = "EmojiBatteryServiceHelper"
    }
    
    // Coroutine scope for background operations
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    
    // Service state tracking
    private var isMonitoringConfig = false
    
    init {
        Log.d(TAG, "EmojiBatteryServiceHelper initialized")
        startConfigurationMonitoring()
    }
    
    /**
     * Starts monitoring configuration changes to automatically manage service state.
     */
    private fun startConfigurationMonitoring() {
        if (isMonitoringConfig) {
            Log.d(TAG, "Configuration monitoring already started")
            return
        }
        
        serviceScope.launch {
            customizationRepository.getCustomizationConfigFlow()
                .catch { exception ->
                    Log.e(TAG, "Error monitoring configuration changes", exception)
                }
                .collect { config ->
                    handleConfigurationChange(config.isGlobalEnabled)
                }
        }
        
        isMonitoringConfig = true
        Log.d(TAG, "Configuration monitoring started")
    }
    
    /**
     * Handles configuration changes and manages service state accordingly.
     */
    private fun handleConfigurationChange(isEnabled: Boolean) {
        Log.d(TAG, "Configuration changed - global enabled: $isEnabled")
        
        if (isEnabled) {
            if (EmojiOverlayPermissionManager.hasAllRequiredPermissions(context)) {
                startService()
            } else {
                Log.w(TAG, "Cannot start service - missing permissions")
            }
        } else {
            stopService()
        }
    }
    
    /**
     * Starts the emoji battery accessibility service.
     */
    fun startService() {
        try {
            if (!EmojiOverlayPermissionManager.hasAllRequiredPermissions(context)) {
                Log.w(TAG, "Cannot start service - missing required permissions")
                return
            }
            
            if (isServiceRunning()) {
                Log.d(TAG, "Service already running")
                return
            }
            
            // The accessibility service is started by the system when enabled
            // We just need to send an intent to show the overlay
            val intent = Intent(context, EmojiBatteryAccessibilityService::class.java).apply {
                action = EmojiBatteryAccessibilityService.ACTION_SHOW_OVERLAY
            }
            
            context.startService(intent)
            Log.d(TAG, "Service start requested")
            
        } catch (exception: Exception) {
            Log.e(TAG, "Error starting service", exception)
        }
    }
    
    /**
     * Stops the emoji battery accessibility service.
     */
    fun stopService() {
        try {
            if (!isServiceRunning()) {
                Log.d(TAG, "Service not running")
                return
            }
            
            // Send intent to hide the overlay
            val intent = Intent(context, EmojiBatteryAccessibilityService::class.java).apply {
                action = EmojiBatteryAccessibilityService.ACTION_HIDE_OVERLAY
            }
            
            context.startService(intent)
            Log.d(TAG, "Service stop requested")
            
        } catch (exception: Exception) {
            Log.e(TAG, "Error stopping service", exception)
        }
    }
    
    /**
     * Updates the service configuration.
     */
    fun updateServiceConfiguration() {
        try {
            if (!isServiceRunning()) {
                Log.d(TAG, "Service not running, cannot update configuration")
                return
            }
            
            val intent = Intent(context, EmojiBatteryAccessibilityService::class.java).apply {
                action = EmojiBatteryAccessibilityService.ACTION_UPDATE_CONFIG
            }
            
            context.startService(intent)
            Log.d(TAG, "Service configuration update requested")
            
        } catch (exception: Exception) {
            Log.e(TAG, "Error updating service configuration", exception)
        }
    }
    
    /**
     * Checks if the emoji battery accessibility service is currently running.
     */
    fun isServiceRunning(): Boolean {
        val isRunning = EmojiBatteryAccessibilityService.isServiceRunning()
        Log.d(TAG, "Service running status: $isRunning")
        return isRunning
    }
    
    /**
     * Gets the current service instance if available.
     */
    fun getServiceInstance(): EmojiBatteryAccessibilityService? {
        return EmojiBatteryAccessibilityService.getInstance()
    }
    
    /**
     * Gets detailed service status for debugging and monitoring.
     */
    fun getServiceStatus(): Map<String, Any> {
        val permissionStatus = EmojiOverlayPermissionManager.getPermissionStatusSummary(context)
        
        return mapOf(
            "isServiceRunning" to isServiceRunning(),
            "isMonitoringConfig" to isMonitoringConfig,
            "hasAllPermissions" to EmojiOverlayPermissionManager.hasAllRequiredPermissions(context),
            "isAccessibilityEnabled" to EmojiOverlayPermissionManager.isAccessibilityServiceEnabled(context),
            "permissionDetails" to permissionStatus
        )
    }
    
    /**
     * Forces a service restart if it's currently enabled.
     */
    fun restartServiceIfEnabled() {
        serviceScope.launch {
            try {
                val config = customizationRepository.getCustomizationConfig()
                if (config.isGlobalEnabled) {
                    Log.d(TAG, "Restarting service as it's enabled in configuration")
                    stopService()
                    // Small delay to ensure service stops
                    kotlinx.coroutines.delay(500)
                    startService()
                } else {
                    Log.d(TAG, "Service not enabled in configuration, not restarting")
                }
            } catch (exception: Exception) {
                Log.e(TAG, "Error restarting service", exception)
            }
        }
    }
    
    /**
     * Handles permission changes and updates service state accordingly.
     */
    fun handlePermissionChange() {
        Log.d(TAG, "Handling permission change")
        
        serviceScope.launch {
            try {
                val config = customizationRepository.getCustomizationConfig()
                handleConfigurationChange(config.isGlobalEnabled)
            } catch (exception: Exception) {
                Log.e(TAG, "Error handling permission change", exception)
            }
        }
    }
    
    /**
     * Cleans up resources when the helper is no longer needed.
     */
    fun cleanup() {
        Log.d(TAG, "Cleaning up EmojiBatteryServiceHelper")
        isMonitoringConfig = false
        // Note: We don't cancel the serviceScope as it might be needed for cleanup operations
    }
}
