package com.tqhit.battery.one.features.emoji.presentation.gallery.adapter

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView

/**
 * ItemDecoration for adding spacing between grid items.
 * Reused from the animation fragment implementation for consistency.
 * 
 * This decoration:
 * - Adds consistent spacing between grid items
 * - Handles edge cases for first/last columns and rows
 * - Maintains proper spacing for different screen sizes
 */
class GridSpacingItemDecoration(
    private val spanCount: Int,
    private val spacing: Int
) : RecyclerView.ItemDecoration() {
    
    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        val position = parent.getChildAdapterPosition(view) // item position
        val column = position % spanCount // item column
        
        // Only add spacing to the right of the left item and to the left of the right item
        outRect.left = if (column == 0) 0 else spacing / 2
        outRect.right = if (column == spanCount - 1) 0 else spacing / 2
        
        // Only add top spacing for rows except the first
        outRect.top = if (position >= spanCount) spacing else 0
        
        // No bottom spacing
        outRect.bottom = 0
    }
}
