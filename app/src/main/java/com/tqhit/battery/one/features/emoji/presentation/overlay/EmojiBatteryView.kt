package com.tqhit.battery.one.features.emoji.presentation.overlay

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View
import androidx.core.content.ContextCompat
import com.tqhit.battery.one.R
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig
import com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus
import com.tqhit.battery.one.utils.BatteryLogger

/**
 * Custom view for displaying emoji battery overlay.
 * Renders the emoji, battery graphic, and percentage text based on current configuration.
 * 
 * This view follows the established patterns in the app:
 * - Uses custom drawing with Canvas and Paint
 * - Integrates with existing battery status data
 * - Supports customization configuration
 * - Follows Material 3 design guidelines
 * - Optimized for overlay display performance
 */
class EmojiBatteryView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {
    
    companion object {
        private const val TAG = "EmojiBatteryView"
        private const val EMOJI_VIEW_TAG = "EmojiView_Rendering"
        private const val EMOJI_DRAW_TAG = "EmojiView_Drawing"
        private const val EMOJI_UPDATE_TAG = "EmojiView_Updates"

        // Default dimensions (in dp, converted to px)
        private const val DEFAULT_HEIGHT_DP = 24
        private const val DEFAULT_EMOJI_SIZE_DP = 20
        private const val DEFAULT_TEXT_SIZE_SP = 12

        // Spacing and padding
        private const val ELEMENT_SPACING_DP = 4
        private const val HORIZONTAL_PADDING_DP = 8
    }
    
    // Paint objects for drawing
    private val textPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        typeface = Typeface.DEFAULT_BOLD
        textAlign = Paint.Align.LEFT
    }
    
    private val batteryPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.FILL
    }
    
    private val batteryStrokePaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.STROKE
        strokeWidth = 2f
    }
    
    // Dimensions (converted from dp to px)
    private val density = resources.displayMetrics.density
    private val defaultHeight = (DEFAULT_HEIGHT_DP * density).toInt()
    private val defaultEmojiSize = (DEFAULT_EMOJI_SIZE_DP * density).toInt()
    private val elementSpacing = (ELEMENT_SPACING_DP * density).toInt()
    private val horizontalPadding = (HORIZONTAL_PADDING_DP * density).toInt()
    
    // Current state
    private var batteryStatus: CoreBatteryStatus? = null
    private var batteryStyle: BatteryStyle? = null
    private var customizationConfig: CustomizationConfig? = null
    
    // Cached bitmaps for performance
    private var emojiBitmap: Bitmap? = null
    private var batteryBitmap: Bitmap? = null
    
    // Drawing rectangles
    private val batteryRect = RectF()
    private val batteryFillRect = RectF()
    private val emojiRect = RectF()
    
    init {
        setupDefaultColors()
        BatteryLogger.d(TAG, "EmojiBatteryView initialized")
        BatteryLogger.d(EMOJI_VIEW_TAG, "EMOJI_VIEW_CREATED")
    }

    /**
     * Sets up default colors based on current theme
     */
    private fun setupDefaultColors() {
        BatteryLogger.d(EMOJI_VIEW_TAG, "Setting up default emoji view colors")

        val defaultTextColor = ContextCompat.getColor(context, R.color.white)
        val defaultBatteryColor = ContextCompat.getColor(context, android.R.color.holo_green_light)
        val defaultStrokeColor = ContextCompat.getColor(context, R.color.white)

        textPaint.color = defaultTextColor
        batteryPaint.color = defaultBatteryColor
        batteryStrokePaint.color = defaultStrokeColor

        BatteryLogger.d(EMOJI_VIEW_TAG, "Default colors set - text: $defaultTextColor, battery: $defaultBatteryColor, stroke: $defaultStrokeColor")
    }
    
    /**
     * Updates the battery status and triggers a redraw
     */
    fun updateBatteryStatus(status: CoreBatteryStatus) {
        BatteryLogger.d(EMOJI_UPDATE_TAG, "Updating emoji battery status: ${status.percentage}%, charging: ${status.isCharging}")
        BatteryLogger.logBatteryStatus(
            EMOJI_UPDATE_TAG,
            status.percentage,
            status.isCharging,
            status.currentMicroAmperes,
            status.voltageMillivolts,
            status.temperatureCelsius
        )

        this.batteryStatus = status
        invalidate()
        BatteryLogger.d(EMOJI_UPDATE_TAG, "EMOJI_BATTERY_STATUS_UPDATED")
    }

    /**
     * Updates the battery style and triggers a redraw
     */
    fun updateBatteryStyle(style: BatteryStyle) {
        BatteryLogger.d(EMOJI_UPDATE_TAG, "Updating emoji battery style: ${style.name}")
        this.batteryStyle = style

        // Clear cached bitmaps to force reload
        emojiBitmap = null
        batteryBitmap = null

        invalidate()
        BatteryLogger.d(EMOJI_UPDATE_TAG, "EMOJI_BATTERY_STYLE_UPDATED: ${style.name}")
    }

    /**
     * Updates the customization configuration and triggers a redraw
     */
    fun updateCustomizationConfig(config: CustomizationConfig) {
        BatteryLogger.d(EMOJI_UPDATE_TAG, "Updating emoji customization config")
        this.customizationConfig = config

        // Update paint properties based on config
        textPaint.color = config.customConfig.percentageColor
        textPaint.textSize = config.customConfig.percentageFontSizeDp * resources.displayMetrics.scaledDensity

        BatteryLogger.d(EMOJI_UPDATE_TAG, "Config updated - show emoji: ${config.customConfig.showEmoji}, show percentage: ${config.customConfig.showPercentage}")

        invalidate()
        BatteryLogger.d(EMOJI_UPDATE_TAG, "EMOJI_CUSTOMIZATION_CONFIG_UPDATED")
    }
    
    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val config = customizationConfig ?: CustomizationConfig.createDefault()
        val status = batteryStatus ?: CoreBatteryStatus.createDefault()

        // Calculate required width based on visible elements
        var totalWidth = horizontalPadding * 2

        // Add emoji width if visible
        if (config.customConfig.showEmoji) {
            totalWidth += (defaultEmojiSize * config.customConfig.emojiSizeScale).toInt() + elementSpacing
        }

        // Add battery width (always shown for now)
        totalWidth += defaultEmojiSize + elementSpacing

        // Add percentage text width if visible
        if (config.customConfig.showPercentage) {
            val percentageText = "${status.percentage}%"
            val textWidth = textPaint.measureText(percentageText)
            totalWidth += textWidth.toInt()
        }

        val width = resolveSize(totalWidth, widthMeasureSpec)
        val height = resolveSize(defaultHeight, heightMeasureSpec)

        setMeasuredDimension(width, height)
    }
    
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        BatteryLogger.v(EMOJI_DRAW_TAG, "Drawing emoji battery view")

        val config = customizationConfig ?: CustomizationConfig.createDefault()
        val status = batteryStatus ?: CoreBatteryStatus.createDefault()

        var currentX = horizontalPadding.toFloat()
        val centerY = height / 2f

        BatteryLogger.v(EMOJI_DRAW_TAG, "Drawing elements - emoji: ${config.customConfig.showEmoji}, percentage: ${config.customConfig.showPercentage}, battery: ${status.percentage}%")

        // Draw emoji if enabled
        if (config.customConfig.showEmoji) {
            currentX += drawEmoji(canvas, currentX, centerY, config)
        }

        // Draw battery graphic
        currentX += drawBattery(canvas, currentX, centerY, status, config)

        // Draw percentage text if enabled
        if (config.customConfig.showPercentage) {
            drawPercentageText(canvas, currentX, centerY, status)
        }

        BatteryLogger.v(EMOJI_DRAW_TAG, "EMOJI_VIEW_DRAW_COMPLETED")
    }
    
    /**
     * Draws the emoji at the specified position
     */
    private fun drawEmoji(canvas: Canvas, x: Float, centerY: Float, config: CustomizationConfig): Float {
        val emojiSize = (defaultEmojiSize * config.customConfig.emojiSizeScale).toInt()
        val emojiTop = centerY - emojiSize / 2f

        // For now, draw a simple circle as placeholder
        // TODO: Load actual emoji bitmap from battery style
        val emojiRadius = emojiSize / 2f
        val emojiCenterX = x + emojiRadius

        // Draw emoji background circle
        batteryPaint.color = ContextCompat.getColor(context, android.R.color.holo_orange_light)
        canvas.drawCircle(emojiCenterX, centerY, emojiRadius, batteryPaint)

        // Draw emoji border
        canvas.drawCircle(emojiCenterX, centerY, emojiRadius, batteryStrokePaint)

        return emojiSize + elementSpacing.toFloat()
    }
    
    /**
     * Draws the battery graphic at the specified position
     */
    private fun drawBattery(canvas: Canvas, x: Float, centerY: Float, status: CoreBatteryStatus, config: CustomizationConfig): Float {
        val batteryWidth = defaultEmojiSize.toFloat()
        val batteryHeight = defaultEmojiSize * 0.6f
        val batteryTop = centerY - batteryHeight / 2f
        
        // Set up battery rectangle
        batteryRect.set(x, batteryTop, x + batteryWidth, batteryTop + batteryHeight)
        
        // Draw battery outline
        batteryStrokePaint.color = ContextCompat.getColor(context, R.color.white)
        canvas.drawRoundRect(batteryRect, 2f, 2f, batteryStrokePaint)
        
        // Draw battery fill based on level
        val fillWidth = batteryWidth * (status.percentage / 100f)
        batteryFillRect.set(x + 1, batteryTop + 1, x + fillWidth - 1, batteryTop + batteryHeight - 1)
        
        // Set battery color based on level and charging status
        val batteryColor = when {
            status.isCharging -> ContextCompat.getColor(context, android.R.color.holo_blue_light)
            status.percentage <= 20 -> ContextCompat.getColor(context, android.R.color.holo_red_light)
            status.percentage <= 50 -> ContextCompat.getColor(context, android.R.color.holo_orange_light)
            else -> ContextCompat.getColor(context, android.R.color.holo_green_light)
        }
        
        batteryPaint.color = batteryColor
        canvas.drawRoundRect(batteryFillRect, 1f, 1f, batteryPaint)
        
        // Draw battery terminal (small rectangle on the right)
        val terminalWidth = 2f
        val terminalHeight = batteryHeight * 0.4f
        val terminalTop = centerY - terminalHeight / 2f
        val terminalRect = RectF(
            x + batteryWidth,
            terminalTop,
            x + batteryWidth + terminalWidth,
            terminalTop + terminalHeight
        )
        canvas.drawRect(terminalRect, batteryStrokePaint)
        
        return batteryWidth + terminalWidth + elementSpacing
    }
    
    /**
     * Draws the percentage text at the specified position
     */
    private fun drawPercentageText(canvas: Canvas, x: Float, centerY: Float, status: CoreBatteryStatus) {
        val percentageText = "${status.percentage}%"

        // Calculate text baseline for vertical centering
        val textMetrics = textPaint.fontMetrics
        val textHeight = textMetrics.descent - textMetrics.ascent
        val textBaseline = centerY + textHeight / 2f - textMetrics.descent

        canvas.drawText(percentageText, x, textBaseline, textPaint)
    }
    
    /**
     * Cleans up emoji view resources
     */
    fun cleanup() {
        BatteryLogger.d(TAG, "Cleaning up EmojiBatteryView resources")
        BatteryLogger.d(EMOJI_VIEW_TAG, "EMOJI_VIEW_CLEANUP_STARTED")

        emojiBitmap?.recycle()
        batteryBitmap?.recycle()
        emojiBitmap = null
        batteryBitmap = null

        BatteryLogger.d(EMOJI_VIEW_TAG, "EMOJI_VIEW_CLEANUP_COMPLETED")
    }
}
