package com.tqhit.battery.one.features.emoji.presentation.overlay

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.util.Log
import android.view.View
import androidx.core.content.ContextCompat
import com.tqhit.battery.one.R
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig
import com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus

/**
 * Custom view for displaying emoji battery overlay.
 * Renders the emoji, battery graphic, and percentage text based on current configuration.
 * 
 * This view follows the established patterns in the app:
 * - Uses custom drawing with Canvas and Paint
 * - Integrates with existing battery status data
 * - Supports customization configuration
 * - Follows Material 3 design guidelines
 * - Optimized for overlay display performance
 */
class EmojiBatteryView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {
    
    companion object {
        private const val TAG = "EmojiBatteryView"
        
        // Default dimensions (in dp, converted to px)
        private const val DEFAULT_HEIGHT_DP = 24
        private const val DEFAULT_EMOJI_SIZE_DP = 20
        private const val DEFAULT_TEXT_SIZE_SP = 12
        
        // Spacing and padding
        private const val ELEMENT_SPACING_DP = 4
        private const val HORIZONTAL_PADDING_DP = 8
    }
    
    // Paint objects for drawing
    private val textPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        typeface = Typeface.DEFAULT_BOLD
        textAlign = Paint.Align.LEFT
    }
    
    private val batteryPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.FILL
    }
    
    private val batteryStrokePaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.STROKE
        strokeWidth = 2f
    }
    
    // Dimensions (converted from dp to px)
    private val density = resources.displayMetrics.density
    private val defaultHeight = (DEFAULT_HEIGHT_DP * density).toInt()
    private val defaultEmojiSize = (DEFAULT_EMOJI_SIZE_DP * density).toInt()
    private val elementSpacing = (ELEMENT_SPACING_DP * density).toInt()
    private val horizontalPadding = (HORIZONTAL_PADDING_DP * density).toInt()
    
    // Current state
    private var batteryStatus: CoreBatteryStatus? = null
    private var batteryStyle: BatteryStyle? = null
    private var customizationConfig: CustomizationConfig? = null
    
    // Cached bitmaps for performance
    private var emojiBitmap: Bitmap? = null
    private var batteryBitmap: Bitmap? = null
    
    // Drawing rectangles
    private val batteryRect = RectF()
    private val batteryFillRect = RectF()
    private val emojiRect = RectF()
    
    init {
        setupDefaultColors()
        Log.d(TAG, "EmojiBatteryView initialized")
    }
    
    /**
     * Sets up default colors based on current theme
     */
    private fun setupDefaultColors() {
        val defaultTextColor = ContextCompat.getColor(context, R.color.white)
        val defaultBatteryColor = ContextCompat.getColor(context, android.R.color.holo_green_light)
        val defaultStrokeColor = ContextCompat.getColor(context, R.color.white)
        
        textPaint.color = defaultTextColor
        batteryPaint.color = defaultBatteryColor
        batteryStrokePaint.color = defaultStrokeColor
    }
    
    /**
     * Updates the battery status and triggers a redraw
     */
    fun updateBatteryStatus(status: CoreBatteryStatus) {
        Log.d(TAG, "Updating battery status: ${status.batteryLevel}%, charging: ${status.isCharging}")
        this.batteryStatus = status
        invalidate()
    }
    
    /**
     * Updates the battery style and triggers a redraw
     */
    fun updateBatteryStyle(style: BatteryStyle) {
        Log.d(TAG, "Updating battery style: ${style.name}")
        this.batteryStyle = style
        // Clear cached bitmaps to force reload
        emojiBitmap = null
        batteryBitmap = null
        invalidate()
    }
    
    /**
     * Updates the customization configuration and triggers a redraw
     */
    fun updateCustomizationConfig(config: CustomizationConfig) {
        Log.d(TAG, "Updating customization config")
        this.customizationConfig = config

        // Update paint properties based on config
        textPaint.color = config.customConfig.percentageColor
        textPaint.textSize = config.customConfig.percentageFontSizeDp * resources.displayMetrics.scaledDensity

        invalidate()
    }
    
    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val config = customizationConfig ?: CustomizationConfig.createDefault()
        val status = batteryStatus ?: CoreBatteryStatus.createDefault()

        // Calculate required width based on visible elements
        var totalWidth = horizontalPadding * 2

        // Add emoji width if visible
        if (config.customConfig.showEmoji) {
            totalWidth += (defaultEmojiSize * config.customConfig.emojiSizeScale).toInt() + elementSpacing
        }

        // Add battery width (always shown for now)
        totalWidth += defaultEmojiSize + elementSpacing

        // Add percentage text width if visible
        if (config.customConfig.showPercentage) {
            val percentageText = "${status.percentage}%"
            val textWidth = textPaint.measureText(percentageText)
            totalWidth += textWidth.toInt()
        }

        val width = resolveSize(totalWidth, widthMeasureSpec)
        val height = resolveSize(defaultHeight, heightMeasureSpec)

        setMeasuredDimension(width, height)
    }
    
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        val config = customizationConfig ?: CustomizationConfig.createDefault()
        val status = batteryStatus ?: CoreBatteryStatus.createDefault()

        var currentX = horizontalPadding.toFloat()
        val centerY = height / 2f

        // Draw emoji if enabled
        if (config.customConfig.showEmoji) {
            currentX += drawEmoji(canvas, currentX, centerY, config)
        }

        // Draw battery graphic
        currentX += drawBattery(canvas, currentX, centerY, status, config)

        // Draw percentage text if enabled
        if (config.customConfig.showPercentage) {
            drawPercentageText(canvas, currentX, centerY, status)
        }
    }
    
    /**
     * Draws the emoji at the specified position
     */
    private fun drawEmoji(canvas: Canvas, x: Float, centerY: Float, config: CustomizationConfig): Float {
        val emojiSize = (defaultEmojiSize * config.customConfig.emojiSizeScale).toInt()
        val emojiTop = centerY - emojiSize / 2f

        // For now, draw a simple circle as placeholder
        // TODO: Load actual emoji bitmap from battery style
        val emojiRadius = emojiSize / 2f
        val emojiCenterX = x + emojiRadius

        // Draw emoji background circle
        batteryPaint.color = ContextCompat.getColor(context, android.R.color.holo_orange_light)
        canvas.drawCircle(emojiCenterX, centerY, emojiRadius, batteryPaint)

        // Draw emoji border
        canvas.drawCircle(emojiCenterX, centerY, emojiRadius, batteryStrokePaint)

        return emojiSize + elementSpacing.toFloat()
    }
    
    /**
     * Draws the battery graphic at the specified position
     */
    private fun drawBattery(canvas: Canvas, x: Float, centerY: Float, status: CoreBatteryStatus, config: CustomizationConfig): Float {
        val batteryWidth = defaultEmojiSize.toFloat()
        val batteryHeight = defaultEmojiSize * 0.6f
        val batteryTop = centerY - batteryHeight / 2f
        
        // Set up battery rectangle
        batteryRect.set(x, batteryTop, x + batteryWidth, batteryTop + batteryHeight)
        
        // Draw battery outline
        batteryStrokePaint.color = ContextCompat.getColor(context, R.color.white)
        canvas.drawRoundRect(batteryRect, 2f, 2f, batteryStrokePaint)
        
        // Draw battery fill based on level
        val fillWidth = batteryWidth * (status.percentage / 100f)
        batteryFillRect.set(x + 1, batteryTop + 1, x + fillWidth - 1, batteryTop + batteryHeight - 1)
        
        // Set battery color based on level and charging status
        val batteryColor = when {
            status.isCharging -> ContextCompat.getColor(context, android.R.color.holo_blue_light)
            status.percentage <= 20 -> ContextCompat.getColor(context, android.R.color.holo_red_light)
            status.percentage <= 50 -> ContextCompat.getColor(context, android.R.color.holo_orange_light)
            else -> ContextCompat.getColor(context, android.R.color.holo_green_light)
        }
        
        batteryPaint.color = batteryColor
        canvas.drawRoundRect(batteryFillRect, 1f, 1f, batteryPaint)
        
        // Draw battery terminal (small rectangle on the right)
        val terminalWidth = 2f
        val terminalHeight = batteryHeight * 0.4f
        val terminalTop = centerY - terminalHeight / 2f
        val terminalRect = RectF(
            x + batteryWidth,
            terminalTop,
            x + batteryWidth + terminalWidth,
            terminalTop + terminalHeight
        )
        canvas.drawRect(terminalRect, batteryStrokePaint)
        
        return batteryWidth + terminalWidth + elementSpacing
    }
    
    /**
     * Draws the percentage text at the specified position
     */
    private fun drawPercentageText(canvas: Canvas, x: Float, centerY: Float, status: CoreBatteryStatus) {
        val percentageText = "${status.percentage}%"

        // Calculate text baseline for vertical centering
        val textMetrics = textPaint.fontMetrics
        val textHeight = textMetrics.descent - textMetrics.ascent
        val textBaseline = centerY + textHeight / 2f - textMetrics.descent

        canvas.drawText(percentageText, x, textBaseline, textPaint)
    }
    
    /**
     * Cleans up resources
     */
    fun cleanup() {
        Log.d(TAG, "Cleaning up EmojiBatteryView resources")
        emojiBitmap?.recycle()
        batteryBitmap?.recycle()
        emojiBitmap = null
        batteryBitmap = null
    }
}
