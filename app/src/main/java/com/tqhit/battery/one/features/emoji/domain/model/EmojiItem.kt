package com.tqhit.battery.one.features.emoji.domain.model

import com.google.gson.annotations.SerializedName
import java.io.Serializable

data class EmojiItem(
    val id: String,
    @SerializedName("category_id")
    val categoryId: String,
    val priority: Int,
    val name: String,
    val thumbnail: String,
    val photo: String,
    val status: Boolean = true,
    @SerializedName("is_pro")
    val isPro: Boolean? = false,
    @SerializedName("custom_fields")
    val customFields: CustomFields
) : Serializable {
}

data class CustomFields(
    val battery: String,
    val emoji: String,
) : Serializable