package com.tqhit.battery.one.features.emoji.presentation.overlay

import android.accessibilityservice.AccessibilityService
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Build
import android.util.Log
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.view.accessibility.AccessibilityEvent
import androidx.lifecycle.lifecycleScope
import com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig
import com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Accessibility service for displaying emoji battery overlay.
 * Uses TYPE_ACCESSIBILITY_OVERLAY for API 26+ and fallback for older versions.
 * 
 * This service follows the established patterns in the app:
 * - Uses Hilt for dependency injection
 * - Integrates with CoreBatteryStatsProvider for battery data
 * - Follows proper lifecycle management
 * - Handles API level compatibility
 * - Supports swipe gestures for notification panel access
 */
@AndroidEntryPoint
class EmojiBatteryAccessibilityService : AccessibilityService() {
    
    companion object {
        private const val TAG = "EmojiBatteryAccessibilityService"
        
        // Service state tracking
        @Volatile
        private var serviceInstance: EmojiBatteryAccessibilityService? = null
        
        /**
         * Gets the current service instance if running
         */
        fun getInstance(): EmojiBatteryAccessibilityService? = serviceInstance
        
        /**
         * Checks if the service is currently running
         */
        fun isServiceRunning(): Boolean = serviceInstance != null
    }
    
    // Dependencies
    @Inject
    lateinit var coreBatteryStatsProvider: CoreBatteryStatsProvider
    
    @Inject
    lateinit var customizationRepository: CustomizationRepository
    
    // UI components
    private var windowManager: WindowManager? = null
    private var overlayView: EmojiBatteryView? = null
    private var layoutParams: WindowManager.LayoutParams? = null
    
    // State management
    private var isOverlayVisible = false
    private var batteryStatusJob: Job? = null
    private var customizationJob: Job? = null
    private var currentConfig: CustomizationConfig? = null
    
    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "EmojiBatteryAccessibilityService onCreate")
        serviceInstance = this
        
        windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
        setupOverlayView()
    }
    
    override fun onServiceConnected() {
        super.onServiceConnected()
        Log.d(TAG, "EmojiBatteryAccessibilityService connected")
        
        startBatteryMonitoring()
        startCustomizationMonitoring()
        showOverlayIfEnabled()
    }
    
    override fun onDestroy() {
        Log.d(TAG, "EmojiBatteryAccessibilityService onDestroy")
        
        stopMonitoring()
        hideOverlay()
        cleanup()
        
        serviceInstance = null
        super.onDestroy()
    }
    
    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        // We don't need to handle accessibility events for this overlay
        // This service is primarily used for overlay permissions
    }
    
    override fun onInterrupt() {
        Log.d(TAG, "EmojiBatteryAccessibilityService interrupted")
        hideOverlay()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "EmojiBatteryAccessibilityService onStartCommand")
        
        when (intent?.action) {
            ACTION_SHOW_OVERLAY -> showOverlay()
            ACTION_HIDE_OVERLAY -> hideOverlay()
            ACTION_UPDATE_CONFIG -> updateConfiguration()
        }
        
        return START_STICKY
    }
    
    /**
     * Sets up the overlay view and layout parameters
     */
    private fun setupOverlayView() {
        try {
            overlayView = EmojiBatteryView(this).apply {
                // Set up touch handling for swipe gestures
                setOnTouchListener { _, event ->
                    handleTouchEvent(event)
                }
            }
            
            layoutParams = createLayoutParams()
            Log.d(TAG, "Overlay view setup completed")
        } catch (exception: Exception) {
            Log.e(TAG, "Error setting up overlay view", exception)
        }
    }
    
    /**
     * Creates layout parameters for the overlay window
     */
    private fun createLayoutParams(): WindowManager.LayoutParams {
        val type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            WindowManager.LayoutParams.TYPE_ACCESSIBILITY_OVERLAY
        } else {
            @Suppress("DEPRECATION")
            WindowManager.LayoutParams.TYPE_PHONE
        }
        
        return WindowManager.LayoutParams(
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            type,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                    WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH,
            PixelFormat.TRANSLUCENT
        ).apply {
            gravity = Gravity.TOP or Gravity.START
            x = 0
            y = 0
        }
    }
    
    /**
     * Starts monitoring battery status changes
     */
    private fun startBatteryMonitoring() {
        batteryStatusJob?.cancel()
        batteryStatusJob = lifecycleScope.launch {
            coreBatteryStatsProvider.coreBatteryStatusFlow
                .catch { exception ->
                    Log.e(TAG, "Error in battery status flow", exception)
                }
                .collect { status ->
                    updateBatteryStatus(status)
                }
        }
        Log.d(TAG, "Battery monitoring started")
    }
    
    /**
     * Starts monitoring customization configuration changes
     */
    private fun startCustomizationMonitoring() {
        customizationJob?.cancel()
        customizationJob = lifecycleScope.launch {
            customizationRepository.getCustomizationConfigFlow()
                .catch { exception ->
                    Log.e(TAG, "Error in customization config flow", exception)
                }
                .collect { config ->
                    updateCustomizationConfig(config)
                }
        }
        Log.d(TAG, "Customization monitoring started")
    }
    
    /**
     * Updates the battery status in the overlay view
     */
    private fun updateBatteryStatus(status: CoreBatteryStatus) {
        overlayView?.updateBatteryStatus(status)
        Log.d(TAG, "Battery status updated: ${status.percentage}%, charging: ${status.isCharging}")
    }
    
    /**
     * Updates the customization configuration
     */
    private fun updateCustomizationConfig(config: CustomizationConfig) {
        currentConfig = config
        overlayView?.updateCustomizationConfig(config)
        
        // Show or hide overlay based on global enabled state
        if (config.isGlobalEnabled && !isOverlayVisible) {
            showOverlay()
        } else if (!config.isGlobalEnabled && isOverlayVisible) {
            hideOverlay()
        }
        
        Log.d(TAG, "Customization config updated, global enabled: ${config.isGlobalEnabled}")
    }
    
    /**
     * Shows the overlay if enabled in configuration
     */
    private fun showOverlayIfEnabled() {
        lifecycleScope.launch {
            try {
                val config = customizationRepository.getCustomizationConfig()
                if (config.isGlobalEnabled) {
                    showOverlay()
                }
            } catch (exception: Exception) {
                Log.e(TAG, "Error checking if overlay should be shown", exception)
            }
        }
    }
    
    /**
     * Shows the overlay view
     */
    private fun showOverlay() {
        if (isOverlayVisible || overlayView == null || layoutParams == null) {
            return
        }
        
        try {
            windowManager?.addView(overlayView, layoutParams)
            isOverlayVisible = true
            Log.d(TAG, "Overlay shown")
        } catch (exception: Exception) {
            Log.e(TAG, "Error showing overlay", exception)
        }
    }
    
    /**
     * Hides the overlay view
     */
    private fun hideOverlay() {
        if (!isOverlayVisible || overlayView == null) {
            return
        }
        
        try {
            windowManager?.removeView(overlayView)
            isOverlayVisible = false
            Log.d(TAG, "Overlay hidden")
        } catch (exception: Exception) {
            Log.e(TAG, "Error hiding overlay", exception)
        }
    }
    
    /**
     * Updates configuration from repository
     */
    private fun updateConfiguration() {
        lifecycleScope.launch {
            try {
                val config = customizationRepository.getCustomizationConfig()
                updateCustomizationConfig(config)
            } catch (exception: Exception) {
                Log.e(TAG, "Error updating configuration", exception)
            }
        }
    }
    
    /**
     * Handles touch events for swipe gestures
     */
    private fun handleTouchEvent(event: MotionEvent): Boolean {
        // TODO: Implement swipe down gesture to open notification panel
        // For now, allow touch events to pass through
        return false
    }
    
    /**
     * Stops all monitoring jobs
     */
    private fun stopMonitoring() {
        batteryStatusJob?.cancel()
        customizationJob?.cancel()
        batteryStatusJob = null
        customizationJob = null
        Log.d(TAG, "Monitoring stopped")
    }
    
    /**
     * Cleans up resources
     */
    private fun cleanup() {
        overlayView?.cleanup()
        overlayView = null
        layoutParams = null
        windowManager = null
        currentConfig = null
        Log.d(TAG, "Cleanup completed")
    }
    
    // Intent actions for service control
    companion object {
        const val ACTION_SHOW_OVERLAY = "com.tqhit.battery.one.emoji.SHOW_OVERLAY"
        const val ACTION_HIDE_OVERLAY = "com.tqhit.battery.one.emoji.HIDE_OVERLAY"
        const val ACTION_UPDATE_CONFIG = "com.tqhit.battery.one.emoji.UPDATE_CONFIG"
    }
}
