package com.tqhit.battery.one.features.emoji.presentation.overlay

import android.accessibilityservice.AccessibilityService
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Build
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.view.accessibility.AccessibilityEvent
import androidx.lifecycle.lifecycleScope
import com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig
import com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus
import com.tqhit.battery.one.utils.BatteryLogger
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Accessibility service for displaying emoji battery overlay.
 * Uses TYPE_ACCESSIBILITY_OVERLAY for API 26+ and fallback for older versions.
 *
 * This service follows the established patterns in the app:
 * - Uses Hilt for dependency injection
 * - Integrates with CoreBatteryStatsProvider for battery data
 * - Uses BatteryLogger for comprehensive logging
 * - Follows proper lifecycle management
 * - Handles API level compatibility
 * - Supports swipe gestures for notification panel access
 * - Avoids conflicts with ChargingOverlayService
 */
@AndroidEntryPoint
class EmojiBatteryAccessibilityService : AccessibilityService() {

    companion object {
        private const val TAG = "EmojiBatteryAccessibilityService"
        private const val EMOJI_SERVICE_TAG = "EmojiService_Lifecycle"
        private const val EMOJI_OVERLAY_TAG = "EmojiService_Overlay"
        private const val EMOJI_BATTERY_TAG = "EmojiService_Battery"
        private const val EMOJI_CONFIG_TAG = "EmojiService_Config"

        // Service state tracking
        @Volatile
        private var emojiServiceInstance: EmojiBatteryAccessibilityService? = null

        /**
         * Gets the current emoji service instance if running
         */
        fun getInstance(): EmojiBatteryAccessibilityService? {
            BatteryLogger.d(EMOJI_SERVICE_TAG, "Getting emoji service instance: ${emojiServiceInstance != null}")
            return emojiServiceInstance
        }

        /**
         * Checks if the emoji service is currently running
         */
        fun isServiceRunning(): Boolean {
            val isRunning = emojiServiceInstance != null
            BatteryLogger.d(EMOJI_SERVICE_TAG, "Emoji service running status: $isRunning")
            return isRunning
        }
    }
    
    // Dependencies
    @Inject
    lateinit var coreBatteryStatsProvider: CoreBatteryStatsProvider
    
    @Inject
    lateinit var customizationRepository: CustomizationRepository
    
    // UI components
    private var emojiWindowManager: WindowManager? = null
    private var emojiOverlayView: EmojiBatteryView? = null
    private var emojiLayoutParams: WindowManager.LayoutParams? = null

    // State management
    private var isEmojiOverlayVisible = false
    private var emojiBatteryStatusJob: Job? = null
    private var emojiCustomizationJob: Job? = null
    private var currentEmojiConfig: CustomizationConfig? = null

    override fun onCreate() {
        super.onCreate()
        BatteryLogger.d(TAG, "EmojiBatteryAccessibilityService onCreate")
        BatteryLogger.d(EMOJI_SERVICE_TAG, "EMOJI_SERVICE_CREATED")

        emojiServiceInstance = this

        emojiWindowManager = getSystemService(WINDOW_SERVICE) as WindowManager
        setupEmojiOverlayView()

        BatteryLogger.d(EMOJI_SERVICE_TAG, "EMOJI_SERVICE_ONCREATE_COMPLETED")
    }
    
    override fun onServiceConnected() {
        super.onServiceConnected()
        BatteryLogger.d(TAG, "EmojiBatteryAccessibilityService connected")
        BatteryLogger.d(EMOJI_SERVICE_TAG, "EMOJI_SERVICE_CONNECTED")

        startEmojiBatteryMonitoring()
        startEmojiCustomizationMonitoring()
        showEmojiOverlayIfEnabled()

        BatteryLogger.d(EMOJI_SERVICE_TAG, "EMOJI_SERVICE_CONNECTION_COMPLETED")
    }

    override fun onDestroy() {
        BatteryLogger.d(TAG, "EmojiBatteryAccessibilityService onDestroy")
        BatteryLogger.d(EMOJI_SERVICE_TAG, "EMOJI_SERVICE_DESTROY_STARTED")

        stopEmojiMonitoring()
        hideEmojiOverlay()
        cleanupEmojiService()

        emojiServiceInstance = null

        BatteryLogger.d(EMOJI_SERVICE_TAG, "EMOJI_SERVICE_DESTROYED")
        super.onDestroy()
    }

    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        // We don't need to handle accessibility events for this emoji overlay
        // This service is primarily used for overlay permissions
        BatteryLogger.v(EMOJI_SERVICE_TAG, "Accessibility event received (ignored for emoji overlay)")
    }

    override fun onInterrupt() {
        BatteryLogger.d(TAG, "EmojiBatteryAccessibilityService interrupted")
        BatteryLogger.d(EMOJI_SERVICE_TAG, "EMOJI_SERVICE_INTERRUPTED")
        hideEmojiOverlay()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        val action = intent?.action ?: "null"
        BatteryLogger.d(TAG, "EmojiBatteryAccessibilityService onStartCommand - action: $action")
        BatteryLogger.d(EMOJI_SERVICE_TAG, "EMOJI_SERVICE_COMMAND_RECEIVED: $action")

        when (intent?.action) {
            ACTION_SHOW_OVERLAY -> {
                BatteryLogger.d(EMOJI_OVERLAY_TAG, "Show emoji overlay command received")
                showEmojiOverlay()
            }
            ACTION_HIDE_OVERLAY -> {
                BatteryLogger.d(EMOJI_OVERLAY_TAG, "Hide emoji overlay command received")
                hideEmojiOverlay()
            }
            ACTION_UPDATE_CONFIG -> {
                BatteryLogger.d(EMOJI_CONFIG_TAG, "Update emoji config command received")
                updateEmojiConfiguration()
            }
            else -> {
                BatteryLogger.w(EMOJI_SERVICE_TAG, "Unknown action received: $action")
            }
        }

        return START_STICKY
    }

    /**
     * Sets up the emoji overlay view and layout parameters
     */
    private fun setupEmojiOverlayView() {
        BatteryLogger.d(TAG, "Setting up emoji overlay view")
        BatteryLogger.d(EMOJI_OVERLAY_TAG, "EMOJI_OVERLAY_SETUP_STARTED")

        try {
            emojiOverlayView = EmojiBatteryView(this).apply {
                // Set up touch handling for swipe gestures
                setOnTouchListener { _, event ->
                    handleEmojiTouchEvent(event)
                }
            }

            emojiLayoutParams = createEmojiLayoutParams()
            BatteryLogger.d(EMOJI_OVERLAY_TAG, "EMOJI_OVERLAY_SETUP_COMPLETED")
        } catch (exception: Exception) {
            BatteryLogger.e(TAG, "Error setting up emoji overlay view", exception)
            BatteryLogger.e(EMOJI_OVERLAY_TAG, "EMOJI_OVERLAY_SETUP_FAILED: ${exception.message}")
        }
    }
    
    /**
     * Creates layout parameters for the emoji overlay window
     */
    private fun createEmojiLayoutParams(): WindowManager.LayoutParams {
        val type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            WindowManager.LayoutParams.TYPE_ACCESSIBILITY_OVERLAY
        } else {
            @Suppress("DEPRECATION")
            WindowManager.LayoutParams.TYPE_PHONE
        }

        BatteryLogger.d(EMOJI_OVERLAY_TAG, "Creating emoji layout params - API ${Build.VERSION.SDK_INT}, type: $type")

        return WindowManager.LayoutParams(
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            type,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                    WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH,
            PixelFormat.TRANSLUCENT
        ).apply {
            gravity = Gravity.TOP or Gravity.START
            x = 0
            y = 0
            BatteryLogger.d(EMOJI_OVERLAY_TAG, "Emoji layout params created - gravity: $gravity, position: ($x, $y)")
        }
    }
    
    /**
     * Starts monitoring emoji battery status changes
     */
    private fun startEmojiBatteryMonitoring() {
        BatteryLogger.d(TAG, "Starting emoji battery monitoring")
        BatteryLogger.d(EMOJI_BATTERY_TAG, "EMOJI_BATTERY_MONITORING_START")

        emojiBatteryStatusJob?.cancel()
        emojiBatteryStatusJob = lifecycleScope.launch {
            coreBatteryStatsProvider.coreBatteryStatusFlow
                .catch { exception ->
                    BatteryLogger.e(TAG, "Error in emoji battery status flow", exception)
                    BatteryLogger.e(EMOJI_BATTERY_TAG, "EMOJI_BATTERY_FLOW_ERROR: ${exception.message}")
                }
                .collect { status ->
                    BatteryLogger.d(EMOJI_BATTERY_TAG, "Emoji battery status update: ${status.percentage}%, charging: ${status.isCharging}")
                    updateEmojiBatteryStatus(status)
                }
        }
        BatteryLogger.d(EMOJI_BATTERY_TAG, "EMOJI_BATTERY_MONITORING_STARTED")
    }

    /**
     * Starts monitoring emoji customization configuration changes
     */
    private fun startEmojiCustomizationMonitoring() {
        BatteryLogger.d(TAG, "Starting emoji customization monitoring")
        BatteryLogger.d(EMOJI_CONFIG_TAG, "EMOJI_CONFIG_MONITORING_START")

        emojiCustomizationJob?.cancel()
        emojiCustomizationJob = lifecycleScope.launch {
            customizationRepository.getCustomizationConfigFlow()
                .catch { exception ->
                    BatteryLogger.e(TAG, "Error in emoji customization config flow", exception)
                    BatteryLogger.e(EMOJI_CONFIG_TAG, "EMOJI_CONFIG_FLOW_ERROR: ${exception.message}")
                }
                .collect { config ->
                    BatteryLogger.d(EMOJI_CONFIG_TAG, "Emoji config update: global enabled: ${config.isGlobalEnabled}")
                    updateEmojiCustomizationConfig(config)
                }
        }
        BatteryLogger.d(EMOJI_CONFIG_TAG, "EMOJI_CONFIG_MONITORING_STARTED")
    }
    
    /**
     * Updates the emoji battery status in the overlay view
     */
    private fun updateEmojiBatteryStatus(status: CoreBatteryStatus) {
        BatteryLogger.logBatteryStatus(
            EMOJI_BATTERY_TAG,
            status.percentage,
            status.isCharging,
            status.currentMicroAmperes,
            status.voltageMillivolts,
            status.temperatureCelsius
        )

        emojiOverlayView?.updateBatteryStatus(status)
        BatteryLogger.d(EMOJI_BATTERY_TAG, "EMOJI_BATTERY_STATUS_UPDATED: ${status.percentage}%, charging: ${status.isCharging}")
    }

    /**
     * Updates the emoji customization configuration
     */
    private fun updateEmojiCustomizationConfig(config: CustomizationConfig) {
        BatteryLogger.d(EMOJI_CONFIG_TAG, "Updating emoji customization config")
        currentEmojiConfig = config
        emojiOverlayView?.updateCustomizationConfig(config)

        // Show or hide emoji overlay based on global enabled state
        if (config.isGlobalEnabled && !isEmojiOverlayVisible) {
            BatteryLogger.d(EMOJI_CONFIG_TAG, "Config enabled and overlay not visible, showing emoji overlay")
            showEmojiOverlay()
        } else if (!config.isGlobalEnabled && isEmojiOverlayVisible) {
            BatteryLogger.d(EMOJI_CONFIG_TAG, "Config disabled and overlay visible, hiding emoji overlay")
            hideEmojiOverlay()
        }

        BatteryLogger.d(EMOJI_CONFIG_TAG, "EMOJI_CONFIG_UPDATED: global enabled: ${config.isGlobalEnabled}")
    }
    
    /**
     * Shows the emoji overlay if enabled in configuration
     */
    private fun showEmojiOverlayIfEnabled() {
        BatteryLogger.d(TAG, "Checking if emoji overlay should be shown")
        BatteryLogger.d(EMOJI_OVERLAY_TAG, "EMOJI_OVERLAY_ENABLED_CHECK")

        lifecycleScope.launch {
            try {
                val config = customizationRepository.getCustomizationConfig()
                BatteryLogger.d(EMOJI_CONFIG_TAG, "Retrieved config - global enabled: ${config.isGlobalEnabled}")
                if (config.isGlobalEnabled) {
                    BatteryLogger.d(EMOJI_OVERLAY_TAG, "Config enabled, showing emoji overlay")
                    showEmojiOverlay()
                } else {
                    BatteryLogger.d(EMOJI_OVERLAY_TAG, "Config disabled, not showing emoji overlay")
                }
            } catch (exception: Exception) {
                BatteryLogger.e(TAG, "Error checking if emoji overlay should be shown", exception)
                BatteryLogger.e(EMOJI_OVERLAY_TAG, "EMOJI_OVERLAY_ENABLED_CHECK_FAILED: ${exception.message}")
            }
        }
    }

    /**
     * Shows the emoji overlay view
     */
    private fun showEmojiOverlay() {
        BatteryLogger.d(TAG, "Showing emoji overlay")
        BatteryLogger.d(EMOJI_OVERLAY_TAG, "EMOJI_OVERLAY_SHOW_REQUESTED")

        if (isEmojiOverlayVisible || emojiOverlayView == null || emojiLayoutParams == null) {
            BatteryLogger.d(EMOJI_OVERLAY_TAG, "Emoji overlay show skipped - visible: $isEmojiOverlayVisible, view: ${emojiOverlayView != null}, params: ${emojiLayoutParams != null}")
            return
        }

        try {
            emojiWindowManager?.addView(emojiOverlayView, emojiLayoutParams)
            isEmojiOverlayVisible = true
            BatteryLogger.d(EMOJI_OVERLAY_TAG, "EMOJI_OVERLAY_SHOWN")
        } catch (exception: Exception) {
            BatteryLogger.e(TAG, "Error showing emoji overlay", exception)
            BatteryLogger.e(EMOJI_OVERLAY_TAG, "EMOJI_OVERLAY_SHOW_FAILED: ${exception.message}")
        }
    }
    
    /**
     * Hides the emoji overlay view
     */
    private fun hideEmojiOverlay() {
        BatteryLogger.d(TAG, "Hiding emoji overlay")
        BatteryLogger.d(EMOJI_OVERLAY_TAG, "EMOJI_OVERLAY_HIDE_REQUESTED")

        if (!isEmojiOverlayVisible || emojiOverlayView == null) {
            BatteryLogger.d(EMOJI_OVERLAY_TAG, "Emoji overlay hide skipped - visible: $isEmojiOverlayVisible, view: ${emojiOverlayView != null}")
            return
        }

        try {
            emojiWindowManager?.removeView(emojiOverlayView)
            isEmojiOverlayVisible = false
            BatteryLogger.d(EMOJI_OVERLAY_TAG, "EMOJI_OVERLAY_HIDDEN")
        } catch (exception: Exception) {
            BatteryLogger.e(TAG, "Error hiding emoji overlay", exception)
            BatteryLogger.e(EMOJI_OVERLAY_TAG, "EMOJI_OVERLAY_HIDE_FAILED: ${exception.message}")
        }
    }

    /**
     * Updates emoji configuration from repository
     */
    private fun updateEmojiConfiguration() {
        BatteryLogger.d(TAG, "Updating emoji configuration from repository")
        BatteryLogger.d(EMOJI_CONFIG_TAG, "EMOJI_CONFIG_REPOSITORY_UPDATE")

        lifecycleScope.launch {
            try {
                val config = customizationRepository.getCustomizationConfig()
                BatteryLogger.d(EMOJI_CONFIG_TAG, "Retrieved updated config from repository")
                updateEmojiCustomizationConfig(config)
            } catch (exception: Exception) {
                BatteryLogger.e(TAG, "Error updating emoji configuration", exception)
                BatteryLogger.e(EMOJI_CONFIG_TAG, "EMOJI_CONFIG_REPOSITORY_UPDATE_FAILED: ${exception.message}")
            }
        }
    }
    
    /**
     * Handles touch events for emoji overlay swipe gestures
     */
    private fun handleEmojiTouchEvent(event: MotionEvent): Boolean {
        BatteryLogger.v(EMOJI_OVERLAY_TAG, "Emoji touch event: ${event.action}")
        // TODO: Implement swipe down gesture to open notification panel
        // For now, allow touch events to pass through
        return false
    }

    /**
     * Stops all emoji monitoring jobs
     */
    private fun stopEmojiMonitoring() {
        BatteryLogger.d(TAG, "Stopping emoji monitoring")
        BatteryLogger.d(EMOJI_SERVICE_TAG, "EMOJI_MONITORING_STOP")

        emojiBatteryStatusJob?.cancel()
        emojiCustomizationJob?.cancel()
        emojiBatteryStatusJob = null
        emojiCustomizationJob = null

        BatteryLogger.d(EMOJI_SERVICE_TAG, "EMOJI_MONITORING_STOPPED")
    }

    /**
     * Cleans up emoji service resources
     */
    private fun cleanupEmojiService() {
        BatteryLogger.d(TAG, "Cleaning up emoji service resources")
        BatteryLogger.d(EMOJI_SERVICE_TAG, "EMOJI_SERVICE_CLEANUP_STARTED")

        emojiOverlayView?.cleanup()
        emojiOverlayView = null
        emojiLayoutParams = null
        emojiWindowManager = null
        currentEmojiConfig = null

        BatteryLogger.d(EMOJI_SERVICE_TAG, "EMOJI_SERVICE_CLEANUP_COMPLETED")
    }
    
    // Intent actions for service control
    companion object {
        const val ACTION_SHOW_OVERLAY = "com.tqhit.battery.one.emoji.SHOW_OVERLAY"
        const val ACTION_HIDE_OVERLAY = "com.tqhit.battery.one.emoji.HIDE_OVERLAY"
        const val ACTION_UPDATE_CONFIG = "com.tqhit.battery.one.emoji.UPDATE_CONFIG"
    }
}
