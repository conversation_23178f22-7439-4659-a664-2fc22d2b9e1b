package com.tqhit.battery.one.features.emoji.domain.model

import java.io.Serializable

/**
 * Domain model representing a battery style configuration for the emoji battery feature.
 * Contains all information needed to display and customize a battery style.
 *
 * @param id Unique identifier for the battery style
 * @param name Display name of the battery style
 * @param category Category this style belongs to (e.g., HOT, Character, Heart, Cute)
 * @param batteryImageUrl URL to the battery container image
 * @param emojiImageUrl URL to the emoji/character image
 * @param isPremium Whether this style requires premium access
 * @param isPopular Whether this style should be featured in popular/trending sections
 * @param defaultConfig Default customization configuration for this style
 * @param timestampEpochMillis When this style was created/last updated
 */
data class BatteryStyle(
    val id: String,
    val name: String,
    val category: BatteryStyleCategory,
    val batteryImageUrl: String,
    val emojiImageUrl: String,
    val isPremium: Boolean = false,
    val isPopular: Boolean = false,
    val defaultConfig: BatteryStyleConfig = BatteryStyleConfig(),
    val timestampEpochMillis: Long = System.currentTimeMillis()
) : Serializable {
    
    /**
     * Validates that the battery style contains valid data.
     * 
     * @return true if all required fields are present and valid
     */
    fun isValid(): Boolean {
        return id.isNotBlank() &&
               name.isNotBlank() &&
               batteryImageUrl.isNotBlank() &&
               emojiImageUrl.isNotBlank() &&
               timestampEpochMillis > 0
    }
    
    /**
     * Creates a preview identifier for caching purposes.
     * 
     * @return Unique preview identifier combining style ID and config
     */
    fun getPreviewId(): String {
        return "${id}_${defaultConfig.hashCode()}"
    }
    
    /**
     * Checks if this style matches the search query.
     * Searches in name and category.
     * 
     * @param query Search query (case-insensitive)
     * @return true if the style matches the query
     */
    fun matchesSearch(query: String): Boolean {
        if (query.isBlank()) return true
        
        val lowerQuery = query.lowercase()
        return name.lowercase().contains(lowerQuery) ||
               category.displayName.lowercase().contains(lowerQuery)
    }
    
    companion object {
        private const val TAG = "BatteryStyle"
        
        /**
         * Creates a default/fallback BatteryStyle when real data is unavailable.
         * This ensures the application always has a valid style object to work with.
         *
         * @return A default BatteryStyle with safe fallback values
         */
        fun createDefault(): BatteryStyle {
            return BatteryStyle(
                id = "default",
                name = "Default Style",
                category = BatteryStyleCategory.CHARACTER,
                batteryImageUrl = "",
                emojiImageUrl = "",
                isPremium = false,
                isPopular = false,
                defaultConfig = BatteryStyleConfig()
            )
        }
    }
}

/**
 * Configuration for customizing a battery style.
 * Contains all user-customizable properties for the emoji battery display.
 *
 * @param showEmoji Whether to display the emoji/character
 * @param showPercentage Whether to display the battery percentage text
 * @param percentageFontSizeDp Font size for percentage text in dp (5-40)
 * @param emojiSizeScale Scale factor for emoji size relative to battery (0.5-2.0)
 * @param percentageColor Color for the percentage text (ARGB format)
 */
data class BatteryStyleConfig(
    val showEmoji: Boolean = true,
    val showPercentage: Boolean = true,
    val percentageFontSizeDp: Int = 14,
    val emojiSizeScale: Float = 1.0f,
    val percentageColor: Int = 0xFFFFFFFF.toInt() // White by default
) : Serializable {

    /**
     * Validates that the configuration contains reasonable values.
     *
     * @return true if all values are within expected ranges
     */
    fun isValid(): Boolean {
        return percentageFontSizeDp in 5..40 &&
               emojiSizeScale in 0.5f..2.0f
    }

    /**
     * Creates a copy with validated values, clamping out-of-range values.
     *
     * @return A validated copy of this configuration
     */
    fun validated(): BatteryStyleConfig {
        return copy(
            percentageFontSizeDp = percentageFontSizeDp.coerceIn(5, 40),
            emojiSizeScale = emojiSizeScale.coerceIn(0.5f, 2.0f)
        )
    }

    companion object {
        /**
         * Creates a default configuration with standard values.
         *
         * @return Default BatteryStyleConfig
         */
        fun createDefault(): BatteryStyleConfig {
            return BatteryStyleConfig()
        }
    }
}

/**
 * Complete customization configuration for the emoji battery feature.
 * This includes the selected battery style and all user customizations.
 * Used for persistence and state management in the customization screen.
 *
 * @param selectedStyleId ID of the currently selected battery style
 * @param customConfig User's customization settings
 * @param isGlobalEnabled Whether the emoji battery feature is globally enabled
 * @param lastModifiedTimestamp When this configuration was last modified
 */
data class CustomizationConfig(
    val selectedStyleId: String = "",
    val customConfig: BatteryStyleConfig = BatteryStyleConfig(),
    val isGlobalEnabled: Boolean = false,
    val lastModifiedTimestamp: Long = System.currentTimeMillis()
) : Serializable {

    /**
     * Validates that the customization configuration is valid.
     *
     * @return true if the configuration is valid
     */
    fun isValid(): Boolean {
        return selectedStyleId.isNotBlank() &&
               customConfig.isValid() &&
               lastModifiedTimestamp > 0
    }

    /**
     * Creates a copy with validated values.
     *
     * @return A validated copy of this configuration
     */
    fun validated(): CustomizationConfig {
        return copy(
            customConfig = customConfig.validated(),
            lastModifiedTimestamp = if (lastModifiedTimestamp <= 0) System.currentTimeMillis() else lastModifiedTimestamp
        )
    }

    /**
     * Creates a copy with updated timestamp.
     *
     * @return A copy with current timestamp
     */
    fun withUpdatedTimestamp(): CustomizationConfig {
        return copy(lastModifiedTimestamp = System.currentTimeMillis())
    }

    companion object {
        /**
         * Creates a default customization configuration.
         *
         * @return Default CustomizationConfig
         */
        fun createDefault(): CustomizationConfig {
            return CustomizationConfig()
        }

        /**
         * Creates a customization configuration for a specific style.
         *
         * @param styleId The ID of the battery style
         * @param config Optional custom configuration
         * @return CustomizationConfig for the specified style
         */
        fun forStyle(styleId: String, config: BatteryStyleConfig = BatteryStyleConfig()): CustomizationConfig {
            return CustomizationConfig(
                selectedStyleId = styleId,
                customConfig = config,
                isGlobalEnabled = false,
                lastModifiedTimestamp = System.currentTimeMillis()
            )
        }
    }
}
